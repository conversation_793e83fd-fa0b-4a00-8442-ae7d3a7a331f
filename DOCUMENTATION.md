# Ratings and Reviews Microservice Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [Setup Instructions](#setup-instructions)
   - [Prerequisites](#prerequisites)
   - [Installation Steps](#installation-steps)
   - [Environment Configuration](#environment-configuration)
   - [Running the Application](#running-the-application)
3. [Project Structure](#project-structure)
4. [Core Features](#core-features)
5. [Tech Stack](#tech-stack)
6. [System Architecture](#system-architecture)
7. [Data Flow](#data-flow)
8. [API Endpoints](#api-endpoints)
9. [Jobs and Background Processing](#jobs-and-background-processing)
10. [Database Schema](#database-schema)
11. [Troubleshooting](#troubleshooting)

## Introduction

The Ratings and Reviews Microservice is a component of the Mumzworld platform that manages product ratings, reviews, and associated media. It provides APIs for customers to submit reviews, administrators to moderate content, and interfaces for displaying product ratings and reviews on the frontend.

The service is built as a Laravel-based microservice utilizing DynamoDB for data storage and Redis for caching and job queues.

## Setup Instructions

### Prerequisites

- <PERSON>er and Docker Compose
- Git
- PHP 8.3+ (for local development outside Docker)
- Composer (for local development outside Docker)

### Installation Steps

Follow these steps to set up the project locally:

1. Clone the repository:

```bash
git clone https://github.com/mumzworld-tech/ratings-and-reviews.git
cd ratings-and-reviews
```

2. Install PHP dependencies:

```bash
composer install
```

3. Copy the environment example file:

```bash
cp .env.example .env
```

4. Generate application key:

```bash
php artisan key:generate
```

### Environment Configuration

Edit the `.env` file with the following key configurations:

1. **Basic Laravel configuration**:

```env
APP_NAME="Ratings and Reviews Service"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost
APP_PORT=7000
```

2. **Database configuration**:

```env
DB_CONNECTION=dynamodb
DYNAMODB_CONNECTION=local
DYNAMODB_TABLE=ratings_and_reviews
DYNAMODB_LOCAL_ENDPOINT=http://dynamodb:8000
```

3. **AWS Configuration** (for DynamoDB Local, these can be dummy values):

```env
AWS_ACCESS_KEY_ID="YOUR_ACCESS_KEY"
AWS_SECRET_ACCESS_KEY="YOUR_SECRET_KEY"
AWS_DEFAULT_REGION=us-east-1
```

4. **Redis Configuration**:

```env
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379
```

5. **Queue Configuration**:

```env
QUEUE_CONNECTION=redis
```

6. **CloudFront Configuration** (for media invalidation):

```env
CLOUDFRONT_DISTRIBUTION_ID=your_distribution_id
CLOUDFRONT_KEY=your_cloudfront_key
CLOUDFRONT_SECRET=your_cloudfront_secret
CLOUDFRONT_REGION=us-east-1
```

7. **Google Translate API** (for review translation):

```env
GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key
GOOGLE_TRANSLATE_ENDPOINT=https://translation.googleapis.com/language/translate/v2
```

### Running the Application

Follow these steps to run the application:

1. Start the Docker services:

```bash
docker compose up -d
```

2. Run DynamoDB migrations:

```bash
php artisan migrate:dynamodb
```

3. Seed the database with initial data:

```bash
php artisan db:seed --class=RatingAndReviewSeeder
```

4. Access the service at:
   - Main API: http://localhost:7001
   - PHPMyAdmin: http://localhost:8080 (if MySQL is configured)
   - Redis Commander: http://localhost:8081
   - DynamoDB Admin: http://localhost:8001

## Project Structure

The project follows Laravel's standard structure with additional components for microservice architecture:

```
app/
├── Console/            # Console commands
├── Http/               # HTTP layer (Controllers, Requests, Resources)
│   ├── Controllers/    # API endpoints
│   ├── Requests/       # Form request validation
│   └── Resources/      # API response transformers
├── Jobs/               # Background jobs
│   ├── InvalidateCloudFrontCache.php
│   └── UpdateProductStatisticsJob.php
├── Models/             # Data models
│   ├── RatingAndReview.php
│   ├── RatingsAndReviewStatistics.php
│   └── User.php
├── Providers/          # Service providers
└── Services/           # Business logic services
    ├── CloudFrontService.php
    ├── MediaUploadService.php
    ├── RatingsAndReviewsStatisticsService.php
    └── TranslationService.php
config/                 # Configuration files
database/
├── factories/          # Model factories for testing
├── migrations_dynamodb/# DynamoDB table migrations
└── seeders/            # Database seeders
docker/                 # Docker configuration
├── application/        # Application container
├── dynamodb/           # DynamoDB local container
├── horizon/            # Laravel Horizon container
└── ...                 # Other service containers
routes/
└── api.php            # API route definitions
```

## Core Features

1. **Ratings and Reviews Management**:
   - Creation, retrieval, and moderation of product reviews
   - Support for media attachments (images)
   - Multilingual reviews (English/Arabic)

2. **Statistics Calculation**:
   - Average ratings
   - Rating distributions
   - Background calculation to maintain performance

3. **Content Moderation**:
   - Admin review workflow
   - Publication status management

4. **Media Management**:
   - Uploading review media
   - CloudFront CDN integration
   - Cache invalidation

5. **Translation Services**:
   - Automatic review translation
   - Google Translate API integration

## Tech Stack

- **Framework**: Laravel 11
- **PHP Version**: 8.2+
- **Databases**:
  - DynamoDB (primary data store)
  - Redis (caching, queues)
- **Background Processing**: Laravel Horizon/Queue
- **Containerization**: Docker
- **Web Server**: FrankenPHP (with PHP-FPM)
- **CDN**: AWS CloudFront
- **Translation**: Google Translate API

## System Architecture

```mermaid
flowchart TB
    Client([Client Applications])
    API[API Layer]
    Services[Service Layer]
    Jobs[Background Jobs]
    DDB[(DynamoDB)]
    Redis[(Redis)]
    CloudFront[CloudFront CDN]
    Google[Google Translate API]
    
    Client <--> API
    API <--> Services
    Services <--> DDB
    Services <--> Redis
    Services --> Jobs
    Jobs <--> DDB
    Jobs <--> Redis
    Jobs --> CloudFront
    Services <--> Google
    CloudFront <--> Client
    
    subgraph "Ratings & Reviews Service"
        API
        Services
        Jobs
    end
```

## Data Flow

### Review Creation and Processing Flow

```mermaid
sequenceDiagram
    participant Client
    participant API as API Controller
    participant Service as Review Service
    participant Media as Media Service
    participant Queue as Redis Queue
    participant Job as Statistics Job
    participant DDB as DynamoDB
    participant CDN as CloudFront CDN
    
    Client->>API: Submit review with media
    API->>Service: Process review data
    Service->>DDB: Save review
    
    alt Has Media Files
        Service->>Media: Upload media files
        Media->>CDN: Store media
        Media-->>Service: Media URLs
        Service->>DDB: Update review with media URLs
    end
    
    Service->>Queue: Dispatch statistics update job
    Queue->>Job: Process statistics calculation
    Job->>DDB: Fetch published reviews
    Job->>Job: Calculate statistics
    Job->>DDB: Update product statistics
    
    Service->>CDN: Invalidate relevant caches
    API-->>Client: Review submission response
```

### Review Moderation Flow

```mermaid
flowchart TD
    A[Customer submits review] --> B[Review stored with 'pending' status]
    B --> C[Admin reviews content]
    
    C -->|Approve| D[Update status to 'published']
    C -->|Reject| E[Update status to 'rejected']
    
    D --> F[Recalculate product statistics]
    D --> G[Invalidate caches]
    E --> G
    
    F --> H[Update product rating display]
    G --> I[Update frontend displays]
```

## API Endpoints

### Customer-Facing Endpoints

- **POST `/api/reviews`**: Create a new review
- **GET `/api/products/{product_id}/reviews`**: Get reviews for a product
- **GET `/api/products/{product_id}/rating`**: Get rating summary for a product
- **GET `/api/reviews/{id}/translate`**: Get translated review
- **POST `/api/products/ratings-summary`**: Get rating summaries for multiple products

### Admin-Facing Endpoints

- **GET `/api/reviews`**: List reviews (with filters)
- **DELETE `/api/reviews/{id}`**: Delete a review
- **PUT `/api/reviews/{id}/publication`**: Update review publication status
- **GET `/api/reviews/pending-check`**: Check for pending reviews

## Jobs and Background Processing

The microservice uses Laravel Horizon to manage background jobs:

1. **UpdateProductStatisticsJob**:
   - Triggered when reviews are added, modified, or have status changes
   - Recalculates product ratings and statistics
   - Stores results in the statistics table

2. **InvalidateCloudFrontCache**:
   - Invalidates CloudFront cache entries for products with updated reviews
   - Ensures customers see the latest reviews and ratings

Jobs are processed via Redis queues with the following queues:
- `default`: General processing
- `statistics`: Product statistics calculation
- `cache-invalidation`: CloudFront invalidation requests

## Database Schema

### DynamoDB Tables

1. **`ratings_and_reviews`**:
   - Primary Key: `review_id` (UUID)
   - GSIs:
     - `user_id-index`
     - `product_id-index`
     - `publication_status-index`
   - Key attributes:
     - `review_id` (String)
     - `user_id` (String)
     - `product_id` (String)
     - `rating` (Number)
     - `original_language` (String)
     - `review_en` (String)
     - `review_ar` (String)
     - `country` (String)
     - `created_at` (String - ISO8601 date)
     - `media` (String - JSON array)
     - `publication_status` (String - enum: pending, published, rejected)

2. **`ratings_and_review_statistics`**:
   - Primary Key: `product_id` (String)
   - Attributes:
     - `rating_count` (Number)
     - `average_rating` (Number)
     - `rating_distribution` (Map - counts per rating)
     - `percentage_distribution` (Map - percentages per rating)
     - `last_calculated_at` (String - ISO8601 date)

## Troubleshooting

### Common Issues

1. **DynamoDB Connection Issues**:
   - Verify DynamoDB Local is running: `docker compose ps`
   - Check endpoint configuration in `.env`
   - Ensure AWS credentials are set (even dummy ones for local)

2. **Media Upload Problems**:
   - Check storage permissions
   - Verify CloudFront configuration in `.env`

3. **Queue Processing Issues**:
   - Ensure Redis is running: `docker compose ps`
   - Check Horizon status: `http://localhost:7001/horizon`
   - Inspect failed jobs in Horizon dashboard

4. **Translation Not Working**:
   - Verify Google Translate API key is valid
   - Check translation service logs

### Logging

Logs are available in:
- Docker container logs: `docker compose logs app`
- Laravel logs: `storage/logs/laravel.log`
- Horizon dashboard for queue monitoring

---

**Built with ❤️ @ Mumzworld**
