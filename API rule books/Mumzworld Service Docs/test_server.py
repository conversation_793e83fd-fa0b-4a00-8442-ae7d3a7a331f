#!/usr/bin/env python3
"""
Test script for the API Documentation MCP Server
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from api_docs_server import get_all_docs, search_content, parse_markdown_sections

def test_basic_functionality():
    """Test basic server functionality"""
    print("🧪 Testing API Documentation MCP Server...")
    
    # Test 1: Check if documentation files are found
    print("\n1️⃣ Testing documentation file discovery...")
    docs = get_all_docs()
    print(f"   Found {len(docs)} documentation files:")
    for doc in docs:
        print(f"   - {doc.name}")
    
    # Test 2: Test markdown parsing
    print("\n2️⃣ Testing markdown parsing...")
    if docs:
        test_file = docs[0]
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()
            sections = parse_markdown_sections(content)
            print(f"   Parsed {len(sections)} sections from {test_file.name}")
            for section_name in list(sections.keys())[:3]:  # Show first 3 sections
                print(f"   - {section_name}")
            if len(sections) > 3:
                print(f"   ... and {len(sections) - 3} more sections")
        except Exception as e:
            print(f"   Error parsing {test_file.name}: {e}")
    
    # Test 3: Test search functionality
    print("\n3️⃣ Testing search functionality...")
    test_queries = ["authentication", "error codes", "pagination", "response structure"]
    
    for query in test_queries:
        print(f"   Testing query: '{query}'")
        if docs:
            test_file = docs[0]
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                score = search_content(query, content, test_file.name, "test_section")
                print(f"   - Score for '{query}': {score}")
            except Exception as e:
                print(f"   - Error testing query '{query}': {e}")
    
    print("\n✅ Basic functionality tests completed!")

def test_server_imports():
    """Test that all server components can be imported"""
    print("\n🔧 Testing server component imports...")
    
    try:
        from api_docs_server import (
            DocumentSearchResult, 
            DocumentInfo, 
            get_all_docs,
            parse_markdown_sections,
            search_content
        )
        print("   ✅ All core components imported successfully")
        
        # Test model instantiation
        result = DocumentSearchResult(
            file_name="test.md",
            title="Test Document", 
            section="Test Section",
            content="Test content",
            relevance_score=5.0
        )
        print("   ✅ DocumentSearchResult model works")
        
        info = DocumentInfo(
            file_name="test.md",
            title="Test Document",
            sections=["Section 1", "Section 2"],
            file_path="/test/path",
            size=100
        )
        print("   ✅ DocumentInfo model works")
        
    except Exception as e:
        print(f"   ❌ Import error: {e}")

if __name__ == "__main__":
    test_server_imports()
    test_basic_functionality()
    
    print("\n🎉 All tests completed!")
    print("\n📋 Next Steps:")
    print("   1. Run the server: python api_docs_server.py")
    print("   2. Configure your MCP client to connect to this server")
    print("   3. Test queries like 'search authentication' or 'get error codes'")
    print("   4. Use the VS Code MCP extension for development")
