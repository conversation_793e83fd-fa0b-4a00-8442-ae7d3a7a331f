# API Error Codes and Messages

## Introduction

This document provides a comprehensive reference for all error codes and messages used across our API ecosystem. Standardized error handling ensures consistent behavior and improves the developer experience when consuming our APIs.

## Error Response Structure

All error responses follow the standard structure:

```json
{
  "status": "error",
  "message": "Human-readable error description",
  "data": null,
  "error_code": "SPECIFIC_ERROR_CODE",
  "error_details": {
    "additional": "context-specific information"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

## HTTP Status Codes

Our APIs use standard HTTP status codes along with custom error codes for specific scenarios.

### Success Status Codes

| Code | Description | Usage |
|------|-------------|-------|
| 200 | OK | Successful GET, PUT, PATCH requests |
| 201 | Created | Successful POST requests that create resources |
| 202 | Accepted | Request accepted for asynchronous processing |
| 204 | No Content | Successful DELETE requests |

### Client Error Status Codes

| Code | Description | Usage |
|------|-------------|-------|
| 400 | Bad Request | Invalid request syntax or parameters |
| 401 | Unauthorized | Authentication required or invalid |
| 403 | Forbidden | Valid authentication but insufficient permissions |
| 404 | Not Found | Resource doesn't exist |
| 405 | Method Not Allowed | HTTP method not supported for this endpoint |
| 409 | Conflict | Resource already exists or conflicts with current state |
| 422 | Unprocessable Entity | Request is well-formed but contains semantic errors |
| 429 | Too Many Requests | Rate limit exceeded |

### Server Error Status Codes

| Code | Description | Usage |
|------|-------------|-------|
| 500 | Internal Server Error | Unexpected server error |
| 502 | Bad Gateway | Invalid response from upstream server |
| 503 | Service Unavailable | Service temporarily unavailable |
| 504 | Gateway Timeout | Timeout waiting for upstream server |

## Custom Error Codes

### Authentication & Authorization Errors

| Error Code | HTTP Status | Message | Description |
|------------|-------------|---------|-------------|
| `UNAUTHORIZED` | 401 | Authentication required | No authentication credentials provided |
| `INVALID_TOKEN` | 401 | Invalid authentication token | Token is malformed, expired, or invalid |
| `TOKEN_EXPIRED` | 401 | Authentication token has expired | Token is valid but has expired |
| `FORBIDDEN` | 403 | Access denied | Valid authentication but insufficient permissions |
| `INSUFFICIENT_PERMISSIONS` | 403 | Insufficient permissions for this action | User lacks required permissions |

### Validation Errors

| Error Code | HTTP Status | Message | Description |
|------------|-------------|---------|-------------|
| `VALIDATION_ERROR` | 422 | Validation failed | One or more fields failed validation |
| `MISSING_REQUIRED_FIELD` | 422 | Required field is missing | A required field was not provided |
| `INVALID_FIELD_VALUE` | 422 | Invalid field value | Field value doesn't meet validation criteria |
| `INVALID_FORMAT` | 422 | Invalid data format | Data format is incorrect (e.g., invalid JSON) |
| `FIELD_TOO_LONG` | 422 | Field value exceeds maximum length | Field value is longer than allowed |
| `FIELD_TOO_SHORT` | 422 | Field value is below minimum length | Field value is shorter than required |

### Resource Errors

| Error Code | HTTP Status | Message | Description |
|------------|-------------|---------|-------------|
| `RESOURCE_NOT_FOUND` | 404 | Resource not found | Requested resource doesn't exist |
| `RESOURCE_ALREADY_EXISTS` | 409 | Resource already exists | Attempting to create a resource that already exists |
| `RESOURCE_CONFLICT` | 409 | Resource conflict | Resource is in a state that conflicts with the request |
| `RESOURCE_LOCKED` | 409 | Resource is locked | Resource is temporarily locked for editing |
| `RESOURCE_DELETED` | 410 | Resource has been deleted | Resource was previously deleted |

### Rate Limiting Errors

| Error Code | HTTP Status | Message | Description |
|------------|-------------|---------|-------------|
| `RATE_LIMIT_EXCEEDED` | 429 | Rate limit exceeded | Too many requests in the given time period |
| `QUOTA_EXCEEDED` | 429 | API quota exceeded | Monthly/daily quota limit reached |
| `CONCURRENT_LIMIT_EXCEEDED` | 429 | Too many concurrent requests | Too many simultaneous requests |

### Business Logic Errors

| Error Code | HTTP Status | Message | Description |
|------------|-------------|---------|-------------|
| `DUPLICATE_REVIEW` | 409 | User has already reviewed this product | User attempting to create multiple reviews for same product |
| `REVIEW_NOT_ALLOWED` | 403 | Review not allowed for this product | Product doesn't allow reviews or user can't review |
| `INVALID_RATING` | 422 | Rating must be between 1 and 5 | Rating value is outside allowed range |
| `REVIEW_TOO_SOON` | 409 | Cannot review product yet | User must wait before reviewing (e.g., after purchase) |
| `PRODUCT_NOT_REVIEWABLE` | 422 | This product cannot be reviewed | Product type doesn't support reviews |

### System Errors

| Error Code | HTTP Status | Message | Description |
|------------|-------------|---------|-------------|
| `INTERNAL_SERVER_ERROR` | 500 | Internal server error | Unexpected server error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable | Service is down for maintenance |
| `DATABASE_ERROR` | 500 | Database operation failed | Database connection or query error |
| `EXTERNAL_SERVICE_ERROR` | 502 | External service error | Dependency service returned an error |
| `TIMEOUT_ERROR` | 504 | Request timeout | Request took too long to process |

## Error Response Examples

### Validation Error with Multiple Fields

```json
{
  "status": "error",
  "message": "Validation failed",
  "data": null,
  "error_code": "VALIDATION_ERROR",
  "error_details": {
    "fields": {
      "rating": [
        "The rating field is required",
        "Rating must be between 1 and 5"
      ],
      "comment": [
        "The comment must be at least 10 characters",
        "The comment may not be greater than 1000 characters"
      ],
      "product_id": [
        "The product_id field is required",
        "Invalid product ID format"
      ]
    }
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### Rate Limiting Error

```json
{
  "status": "error",
  "message": "Rate limit exceeded",
  "data": null,
  "error_code": "RATE_LIMIT_EXCEEDED",
  "error_details": {
    "limit": 100,
    "remaining": 0,
    "reset_time": "2025-07-31T13:00:00Z",
    "retry_after": 3600
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### Business Logic Error

```json
{
  "status": "error",
  "message": "User has already reviewed this product",
  "data": null,
  "error_code": "DUPLICATE_REVIEW",
  "error_details": {
    "existing_review_id": "rev_123456",
    "product_id": "prod_789",
    "user_id": "user_456"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### System Error

```json
{
  "status": "error",
  "message": "Service temporarily unavailable",
  "data": null,
  "error_code": "SERVICE_UNAVAILABLE",
  "error_details": {
    "reason": "Scheduled maintenance",
    "estimated_recovery": "2025-07-31T14:00:00Z",
    "status_page": "https://status.example.com"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

## Error Handling Best Practices

### For API Developers

1. **Always use appropriate HTTP status codes** that match the error type
2. **Provide clear, actionable error messages** that help developers understand what went wrong
3. **Include relevant error details** without exposing sensitive information
4. **Use consistent error codes** across all endpoints
5. **Log errors appropriately** for debugging and monitoring
6. **Never expose internal system details** in error messages

### For API Consumers

1. **Check the HTTP status code first** to understand the error category
2. **Use the error_code field** for programmatic error handling
3. **Display the message field** to end users when appropriate
4. **Handle rate limiting errors** by implementing retry logic with backoff
5. **Log error_details** for debugging purposes
6. **Use request_id** when reporting issues to support

## Rate Limiting Error Headers

When rate limiting errors occur, additional headers are included:

```http
HTTP/1.1 429 Too Many Requests
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 0
X-RateLimit-Reset: 1627804800
Retry-After: 3600
```

## Localization

Error messages can be localized based on the `Accept-Language` header:

```http
Accept-Language: ar-AE
```

Response:
```json
{
  "status": "error",
  "message": "المراجعة مطلوبة",
  "message_en": "Review is required",
  "error_code": "MISSING_REQUIRED_FIELD"
}
```

## Monitoring and Alerting

Error codes are categorized for monitoring:

- **4xx errors**: Client-side issues (usually don't require immediate attention)
- **5xx errors**: Server-side issues (require immediate investigation)
- **Business logic errors**: May require product team review
- **Rate limiting errors**: May indicate need for capacity planning

## Testing Error Scenarios

When implementing APIs, ensure you test:

1. ✅ All validation scenarios
2. ✅ Authentication and authorization failures
3. ✅ Resource not found scenarios
4. ✅ Rate limiting behavior
5. ✅ Network timeout scenarios
6. ✅ Database connection failures
7. ✅ External service failures

---

*See also: [Response Structures](response_structures.md), [Authentication](authentication.md)*
