# API Version Migration Guide

This guide helps you migrate between different versions of the Mumzworld Ratings and Reviews API.

## Overview

The API supports multiple versions to ensure backward compatibility while introducing new features and improvements. Version negotiation is handled through HTTP headers.

## Supported Versions

- **v1**: Legacy format (default) - maintains backward compatibility
- **v2**: Enhanced format with metadata and improved structure

## Version Negotiation

### 1. API-Version Header (Recommended)

```http
GET /api/reviews/pending-check
API-Version: v2
```

### 2. Accept Header with Media Type

```http
GET /api/reviews/pending-check
Accept: application/vnd.mumzworld.v2+json
```

### 3. Default Behavior

If no version is specified, the API defaults to `v1` for backward compatibility.

## Response Format Differences

### V1 Format (Legacy)

V1 responses return data in the original format:

```json
{
  "has_pending_reviews": true
}
```

### V2 Format (Enhanced)

V2 responses wrap data in a structured format with metadata:

```json
{
  "data": {
    "has_pending_reviews": true
  },
  "meta": {
    "version": "v2",
    "timestamp": "2024-03-15T12:34:56Z",
    "request_id": "req_123456"
  },
  "links": {
    "self": "/api/reviews/pending-check"
  }
}
```

## Response Headers

All API responses include version-related headers:

- `API-Version`: The version used for the response
- `API-Supported-Versions`: Comma-separated list of supported versions
- `API-Deprecation-Warning`: Present when using a deprecated version

## Migration Steps

### From V1 to V2

1. **Update Request Headers**

   Add the API version header to your requests:

   ```http
   API-Version: v2
   ```

2. **Update Response Parsing**

   V2 responses wrap the actual data in a `data` property:

   **Before (V1):**

   ```javascript
   const hasPending = response.has_pending_reviews;
   ```

   **After (V2):**

   ```javascript
   const hasPending = response.data.has_pending_reviews;
   ```

3. **Utilize Enhanced Metadata**

   V2 provides additional metadata you can use:

   ```javascript
   const version = response.meta.version;
   const timestamp = response.meta.timestamp;
   const requestId = response.meta.request_id;
   ```

4. **Handle Pagination Links**

   V2 includes structured pagination links:

   ```javascript
   const nextPageUrl = response.links.next;
   const selfUrl = response.links.self;
   ```

## Error Handling

### Unsupported Version Error

If you request an unsupported version, you'll receive a 406 response:

```json
{
  "error": {
    "message": "API version 'v99' is not supported.",
    "type": "UnsupportedVersionException",
    "code": "UNSUPPORTED_VERSION",
    "supported_versions": ["v1", "v2"]
  }
}
```

### Version-Aware Error Responses

Error responses are also versioned:

**V1 Error:**

```json
{
  "error": {
    "message": "Review not found",
    "code": "REVIEW_NOT_FOUND"
  }
}
```

**V2 Error:**

```json
{
  "data": {
    "error": {
      "message": "Review not found",
      "code": "REVIEW_NOT_FOUND"
    }
  },
  "meta": {
    "version": "v2",
    "timestamp": "2024-03-15T12:34:56Z"
  },
  "links": {
    "self": "/api/reviews/nonexistent-id"
  }
}
```

## Best Practices

1. **Always Specify Version**: Don't rely on the default version for new integrations
2. **Handle Version Headers**: Check response headers to confirm the version used
3. **Graceful Degradation**: Handle unsupported version errors gracefully
4. **Monitor Deprecation Warnings**: Watch for deprecation headers and plan migrations
5. **Test Both Formats**: Ensure your code works with both V1 and V2 during migration

## Code Examples

### JavaScript/Node.js

```javascript
// Using fetch with version header
const response = await fetch('/api/reviews/pending-check', {
  headers: {
    'API-Version': 'v2',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();

// Check version used
const apiVersion = response.headers.get('API-Version');
console.log(`Response using API version: ${apiVersion}`);

// Handle different response formats
if (apiVersion === 'v2') {
  const hasPending = data.data.has_pending_reviews;
  const timestamp = data.meta.timestamp;
} else {
  const hasPending = data.has_pending_reviews;
}
```

### cURL

```bash
# Request V2 format
curl -H "API-Version: v2" \
     -H "Content-Type: application/json" \
     https://api.example.com/api/reviews/pending-check

# Alternative using Accept header
curl -H "Accept: application/vnd.mumzworld.v2+json" \
     https://api.example.com/api/reviews/pending-check
```

### PHP

```php
// Using Guzzle HTTP client
$client = new \GuzzleHttp\Client();

$response = $client->get('/api/reviews/pending-check', [
    'headers' => [
        'API-Version' => 'v2',
        'Content-Type' => 'application/json'
    ]
]);

$data = json_decode($response->getBody(), true);
$apiVersion = $response->getHeader('API-Version')[0];

if ($apiVersion === 'v2') {
    $hasPending = $data['data']['has_pending_reviews'];
} else {
    $hasPending = $data['has_pending_reviews'];
}
```

## Testing Your Migration

1. **Test with V1**: Ensure existing functionality still works
2. **Test with V2**: Verify new format parsing works correctly
3. **Test Error Cases**: Ensure error handling works for both versions
4. **Test Headers**: Verify version headers are being sent and received
5. **Test Unsupported Versions**: Ensure graceful handling of 406 responses

## Support

For questions about API versioning or migration assistance, please contact the API team or refer to the main API documentation.
