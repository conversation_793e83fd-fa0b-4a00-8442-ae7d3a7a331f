# API Versioning Strategy

## Introduction

This document outlines our API versioning strategy to ensure backward compatibility, smooth migrations, and a predictable upgrade path for API consumers. Proper versioning allows us to evolve our APIs while maintaining stability for existing integrations.

## Versioning Philosophy

Our versioning strategy is based on these principles:

1. **Backward Compatibility**: Avoid breaking changes within major versions
2. **Predictable Evolution**: Clear communication about changes and timelines
3. **Graceful Deprecation**: Sufficient notice and migration paths for deprecated features
4. **Semantic Versioning**: Use semantic versioning principles adapted for APIs
5. **Consumer-Centric**: Minimize disruption to API consumers

## Versioning Scheme

We use a modified semantic versioning scheme: `v{MAJOR}.{MINOR}.{PATCH}`

### Version Format

```
v1.2.3
```

Where:
- **MAJOR** (v1, v2, v3): Breaking changes that require consumer updates
- **MINOR** (v1.1, v1.2): New features that are backward compatible
- **PATCH** (v1.1.1, v1.1.2): Bug fixes and minor improvements

### Version Examples

| Version | Type | Description |
|---------|------|-------------|
| `v1.0.0` | Major | Initial stable release |
| `v1.1.0` | Minor | Added new optional fields to responses |
| `v1.1.1` | Patch | Fixed response format bug |
| `v2.0.0` | Major | Changed authentication method (breaking) |
| `v2.1.0` | Minor | Added new endpoints |

## Versioning Methods

### 1. URL Path Versioning (Primary)

Version is specified in the URL path:

```http
GET /api/v1/reviews
GET /api/v2/reviews
```

**Advantages:**
- Clear and explicit
- Easy to cache
- Visible in logs
- RESTful

**Usage:**
```http
GET /api/v1/reviews/123
POST /api/v2/reviews
PUT /api/v1/reviews/123
DELETE /api/v1/reviews/123
```

### 2. Header Versioning (Secondary)

Version specified in HTTP headers:

```http
GET /api/reviews
API-Version: v1
Accept: application/vnd.mumz.v1+json
```

**Advantages:**
- Clean URLs
- Content negotiation
- Multiple versions per resource

**Usage:**
```http
GET /api/reviews
API-Version: v2
Accept: application/vnd.mumz.v2+json
```

### 3. Query Parameter Versioning (Fallback)

Version specified as query parameter:

```http
GET /api/reviews?version=v1
```

**Note:** This method is supported for backward compatibility but not recommended for new implementations.

## Version Lifecycle

### Development Phases

```mermaid
graph LR
    A[Alpha] --> B[Beta]
    B --> C[Release Candidate]
    C --> D[Stable]
    D --> E[Deprecated]
    E --> F[Sunset]
```

### Phase Descriptions

| Phase | Description | Stability | Support |
|-------|-------------|-----------|---------|
| **Alpha** | Early development, frequent changes | Low | Best effort |
| **Beta** | Feature complete, API may change | Medium | Testing support |
| **Release Candidate** | Production ready, minor changes only | High | Full support |
| **Stable** | Production ready, no breaking changes | Very High | Full support |
| **Deprecated** | Marked for removal, use discouraged | Very High | Limited support |
| **Sunset** | No longer available | N/A | No support |

### Version Support Timeline

```
v1.0.0 Stable ────────────── Deprecated ─── Sunset
       │                    │              │
       │ ← 18 months →       │ ← 6 months → │
       │                    │              │
v2.0.0 ─────── Stable ───────────────────── Deprecated
              │                            │
              │ ← 18 months →               │
```

## Breaking vs Non-Breaking Changes

### Breaking Changes (Require Major Version)

❌ **Response Structure Changes:**
```json
// v1 - Breaking change
{
  "reviews": [...] 
}

// v2 - Different structure
{
  "data": {
    "reviews": [...]
  }
}
```

❌ **Required Field Changes:**
```json
// v1
{
  "rating": 5
}

// v2 - Breaking: rating became required
{
  "rating": 5,
  "comment": "Required field added"
}
```

❌ **Field Type Changes:**
```json
// v1
{
  "created_at": "2025-07-31T12:00:00Z"
}

// v2 - Breaking: string to timestamp
{
  "created_at": 1627804800
}
```

❌ **Endpoint Removal:**
```http
# v1 - Available
GET /api/v1/reviews/legacy

# v2 - Breaking: endpoint removed
# 404 Not Found
```

### Non-Breaking Changes (Minor/Patch Version)

✅ **Adding Optional Fields:**
```json
// v1.0
{
  "id": "rev_123",
  "rating": 5
}

// v1.1 - Non-breaking: new optional field
{
  "id": "rev_123",
  "rating": 5,
  "helpful_count": 10  // New optional field
}
```

✅ **Adding New Endpoints:**
```http
# v1.0
GET /api/v1/reviews

# v1.1 - Non-breaking: new endpoint
GET /api/v1/reviews/statistics  # New endpoint
```

✅ **Adding New Optional Parameters:**
```http
# v1.0
GET /api/v1/reviews?page=1

# v1.1 - Non-breaking: new optional parameter
GET /api/v1/reviews?page=1&sort=date  # New optional parameter
```

## Version Implementation

### URL Structure

```
https://api.mumz.com/ratings-and-reviews/v{major}/endpoint
```

Examples:
```
https://api.mumz.com/ratings-and-reviews/v1/reviews
https://api.mumz.com/ratings-and-reviews/v1/reviews/123
https://api.mumz.com/ratings-and-reviews/v2/reviews
```

### Header Implementation

```http
GET /api/reviews HTTP/1.1
Host: api.mumz.com
API-Version: v2
Accept: application/vnd.mumz.v2+json
Content-Type: application/json
```

### Response Headers

All responses include version information:

```http
HTTP/1.1 200 OK
API-Version: v2
Content-Type: application/vnd.mumz.v2+json
Deprecation: true  # If deprecated
Sunset: "2025-12-31T23:59:59Z"  # If being sunset
```

## Migration Guidelines

### For API Providers

1. **Plan Breaking Changes Early**
   - Communicate changes 6+ months in advance
   - Provide migration guides
   - Offer parallel support periods

2. **Implement Graceful Degradation**
   ```php
   // Example: Gradual field migration
   public function getReview($id, $version)
   {
       $review = Review::find($id);
       
       if ($version === 'v1') {
           return $this->formatV1Response($review);
       }
       
       return $this->formatV2Response($review);
   }
   ```

3. **Use Feature Flags**
   ```php
   // Gradual rollout of new version
   if (FeatureFlag::isEnabled('api_v2', $user)) {
       return $this->handleV2Request($request);
   }
   
   return $this->handleV1Request($request);
   ```

### For API Consumers

1. **Version Pin Strategy**
   ```javascript
   // Pin to specific version
   const API_BASE_URL = 'https://api.mumz.com/ratings-and-reviews/v1';
   
   // Don't use 'latest' in production
   // const API_BASE_URL = 'https://api.mumz.com/ratings-and-reviews/latest'; // ❌
   ```

2. **Gradual Migration**
   ```javascript
   // Feature-flag driven migration
   const apiVersion = featureFlags.useV2API ? 'v2' : 'v1';
   const response = await fetch(`/api/${apiVersion}/reviews`);
   ```

3. **Error Handling for Version Changes**
   ```javascript
   try {
     const response = await fetch('/api/v1/reviews');
     
     if (response.status === 410) { // Gone
       // Version no longer supported
       throw new Error('API version no longer supported');
     }
     
   } catch (error) {
     // Fallback or migration logic
   }
   ```

## Deprecation Process

### Step 1: Announce Deprecation

```http
HTTP/1.1 200 OK
Deprecation: true
Sunset: "2025-12-31T23:59:59Z"
Warning: 299 - "API version v1 is deprecated. Migrate to v2 by 2025-12-31"
```

### Step 2: Update Documentation

```markdown
## ⚠️ Deprecation Notice

**API v1 is deprecated** and will be sunset on December 31, 2025.

### Migration Path
- Migrate to v2 before sunset date
- See [Migration Guide](migration-v1-to-v2.md)
- Contact support for assistance

### Timeline
- **Now**: v1 deprecated, v2 available
- **December 31, 2025**: v1 sunset
```

### Step 3: Monitor Usage

```json
{
  "version": "v1",
  "usage_metrics": {
    "daily_requests": 1000,
    "unique_consumers": 5,
    "deprecation_warnings_sent": 1000
  },
  "migration_status": "in_progress"
}
```

### Step 4: Gradual Shutdown

```php
// Implement gradual restrictions
if ($version === 'v1' && $this->isSunsetPhase()) {
    // Reduce rate limits
    $rateLimit = $this->getSunsetRateLimit();
    
    if ($this->exceedsRateLimit($rateLimit)) {
        return response()->json([
            'status' => 'error',
            'message' => 'Rate limit exceeded for deprecated version',
            'error_code' => 'DEPRECATED_VERSION_RATE_LIMIT'
        ], 429);
    }
}
```

## Error Responses for Versioning

### Unsupported Version

```json
{
  "status": "error",
  "message": "API version not supported",
  "data": null,
  "error_code": "UNSUPPORTED_VERSION",
  "error_details": {
    "requested_version": "v3",
    "supported_versions": ["v1", "v2"],
    "latest_version": "v2"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v2",
    "environment": "production"
  }
}
```

### Deprecated Version Warning

```json
{
  "status": "success",
  "message": "Reviews retrieved successfully",
  "data": [...],
  "deprecation_warning": {
    "message": "This API version is deprecated",
    "sunset_date": "2025-12-31T23:59:59Z",
    "migration_guide": "https://docs.api.mumz.com/migration/v1-to-v2"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### Sunset Version

```json
{
  "status": "error",
  "message": "API version no longer supported",
  "data": null,
  "error_code": "VERSION_SUNSET",
  "error_details": {
    "sunset_date": "2025-12-31T23:59:59Z",
    "current_version": "v2",
    "migration_guide": "https://docs.api.mumz.com/migration/v1-to-v2"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

## Testing Version Compatibility

### Backward Compatibility Tests

```php
class VersionCompatibilityTest extends TestCase
{
    /** @test */
    public function v1_review_structure_remains_compatible()
    {
        $response = $this->get('/api/v1/reviews/123');
        
        $response->assertJsonStructure([
            'status',
            'message',
            'data' => [
                'id',
                'rating',
                'comment',
                'created_at'
            ]
        ]);
        
        // Ensure no required fields removed
        $this->assertArrayHasKey('rating', $response->json('data'));
    }
    
    /** @test */
    public function v2_maintains_v1_core_functionality()
    {
        $v1Response = $this->get('/api/v1/reviews/123');
        $v2Response = $this->get('/api/v2/reviews/123');
        
        // Core data should be equivalent
        $this->assertEquals(
            $v1Response->json('data.rating'),
            $v2Response->json('data.rating')
        );
    }
}
```

### Version Header Tests

```javascript
// Test version negotiation
describe('API Version Headers', () => {
  test('should respect API-Version header', async () => {
    const response = await fetch('/api/reviews', {
      headers: {
        'API-Version': 'v2',
        'Accept': 'application/vnd.mumz.v2+json'
      }
    });
    
    expect(response.headers.get('API-Version')).toBe('v2');
  });
  
  test('should default to latest stable version', async () => {
    const response = await fetch('/api/reviews');
    
    expect(response.headers.get('API-Version')).toBe('v2');
  });
});
```

## Documentation Strategy

### Version-Specific Documentation

```
docs/
├── api/
│   ├── v1/
│   │   ├── reviews.md
│   │   ├── statistics.md
│   │   └── changelog.md
│   ├── v2/
│   │   ├── reviews.md
│   │   ├── statistics.md
│   │   └── changelog.md
│   └── migration/
│       ├── v1-to-v2.md
│       └── breaking-changes.md
```

### OpenAPI Specification

```yaml
# swagger.yaml
openapi: 3.0.0
info:
  title: Ratings and Reviews API
  version: 2.0.0
  description: |
    ## Version Information
    - **Current Version**: v2.0.0
    - **Previous Versions**: v1.x (deprecated)
    - **Sunset Date**: v1 will be sunset on 2025-12-31
    
servers:
  - url: https://api.mumz.com/ratings-and-reviews/v2
    description: Version 2 (Current)
  - url: https://api.mumz.com/ratings-and-reviews/v1
    description: Version 1 (Deprecated)
```

## Monitoring and Metrics

### Key Metrics

1. **Version Usage Distribution**
2. **Migration Progress**
3. **Deprecation Warning Responses**
4. **Error Rates by Version**
5. **Performance by Version**

### Dashboard Example

```json
{
  "version_metrics": {
    "v1": {
      "daily_requests": 5000,
      "unique_consumers": 12,
      "error_rate": "0.5%",
      "avg_response_time": "120ms",
      "status": "deprecated"
    },
    "v2": {
      "daily_requests": 15000,
      "unique_consumers": 45,
      "error_rate": "0.2%",
      "avg_response_time": "95ms",
      "status": "stable"
    }
  }
}
```

---

*See also: [Response Structures](response_structures.md), [Error Codes and Messages](error_codes.md)*
