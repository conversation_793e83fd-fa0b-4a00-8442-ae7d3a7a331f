# API Response Structures

## Introduction

This document defines the standardized response structure that all APIs in our microservices architecture must follow. Consistent response formats ensure predictable behavior for API consumers and simplify integration across different services.

## Core Principles

1. **Consistency**: All APIs return responses in the same format
2. **Predictability**: Developers know exactly what to expect
3. **Extensibility**: Structure allows for future enhancements
4. **Clarity**: Clear distinction between success and error states
5. **Metadata**: Rich context information for debugging and monitoring

## Standard Response Structure

Every API response follows this structure:

```json
{
  "status": "success|error",
  "message": "Human-readable description",
  "data": "Actual response data or null",
  "pagination": {
    "current_page": 1,
    "per_page": 15,
    "total": 100,
    "total_pages": 7,
    "has_next": true,
    "has_previous": false,
    "next_page_url": "https://api.example.com/resource?page=2",
    "previous_page_url": null
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

## Response Fields

### Required Fields

| Field | Type | Description |
|-------|------|-------------|
| `status` | string | Either "success" or "error" |
| `message` | string | Human-readable description of the result |
| `data` | any\|null | The actual response data or null for errors |
| `meta` | object | Metadata about the response |

### Optional Fields

| Field | Type | Description |
|-------|------|-------------|
| `pagination` | object | Pagination information (only for paginated responses) |
| `error_code` | string | Specific error code (only for error responses) |
| `error_details` | object | Additional error information (only for error responses) |

### Meta Object Structure

The `meta` object always includes:

```json
{
  "timestamp": "2025-07-31T12:00:00Z",
  "request_id": "req_12345678",
  "service": "ratings-and-reviews",
  "version": "v1",
  "environment": "production"
}
```

## Success Responses

### Single Resource

```json
{
  "status": "success",
  "message": "Review retrieved successfully",
  "data": {
    "id": "rev_123456",
    "user_id": "user_789",
    "product_id": "prod_456",
    "rating": 5,
    "comment": "Excellent product!",
    "created_at": "2025-07-31T10:30:00Z",
    "updated_at": "2025-07-31T10:30:00Z"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### Collection (List)

```json
{
  "status": "success",
  "message": "Reviews retrieved successfully",
  "data": [
    {
      "id": "rev_123456",
      "user_id": "user_789",
      "product_id": "prod_456",
      "rating": 5,
      "comment": "Excellent product!"
    },
    {
      "id": "rev_123457",
      "user_id": "user_790",
      "product_id": "prod_456",
      "rating": 4,
      "comment": "Good quality"
    }
  ],
  "pagination": {
    "current_page": 1,
    "per_page": 15,
    "total": 42,
    "total_pages": 3,
    "has_next": true,
    "has_previous": false,
    "next_page_url": "https://api.example.com/reviews?page=2",
    "previous_page_url": null
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### Empty Collection

```json
{
  "status": "success",
  "message": "No reviews found",
  "data": [],
  "pagination": {
    "current_page": 1,
    "per_page": 15,
    "total": 0,
    "total_pages": 0,
    "has_next": false,
    "has_previous": false,
    "next_page_url": null,
    "previous_page_url": null
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### Creation Success

```json
{
  "status": "success",
  "message": "Review created successfully",
  "data": {
    "id": "rev_123456",
    "user_id": "user_789",
    "product_id": "prod_456",
    "rating": 5,
    "comment": "Excellent product!",
    "created_at": "2025-07-31T12:00:00Z",
    "updated_at": "2025-07-31T12:00:00Z"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

## Error Responses

### Validation Error

```json
{
  "status": "error",
  "message": "Validation failed",
  "data": null,
  "error_code": "VALIDATION_ERROR",
  "error_details": {
    "fields": {
      "rating": ["The rating field is required"],
      "comment": ["The comment must be at least 10 characters"]
    }
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### Resource Not Found

```json
{
  "status": "error",
  "message": "Review not found",
  "data": null,
  "error_code": "RESOURCE_NOT_FOUND",
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### Authentication Error

```json
{
  "status": "error",
  "message": "Authentication required",
  "data": null,
  "error_code": "UNAUTHORIZED",
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### Server Error

```json
{
  "status": "error",
  "message": "Internal server error",
  "data": null,
  "error_code": "INTERNAL_SERVER_ERROR",
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

## Pagination Guidelines

For paginated responses, always include:

- `current_page`: Current page number (1-indexed)
- `per_page`: Number of items per page
- `total`: Total number of items available
- `total_pages`: Total number of pages
- `has_next`: Boolean indicating if there's a next page
- `has_previous`: Boolean indicating if there's a previous page
- `next_page_url`: Full URL for the next page (null if none)
- `previous_page_url`: Full URL for the previous page (null if none)

## Implementation Notes

### DynamoDB Pagination

For DynamoDB services, use `LastEvaluatedKey` for cursor-based pagination:

```json
{
  "pagination": {
    "current_page": 1,
    "per_page": 15,
    "has_next": true,
    "has_previous": false,
    "next_page_token": "eyJpZCI6eyJTIjoicmV2XzEyMzQ1NiJ9fQ==",
    "previous_page_token": null
  }
}
```

### PostgreSQL Pagination

For PostgreSQL services, use offset-based pagination with total counts.

### Response Headers

Always include these HTTP headers:

- `Content-Type: application/json`
- `X-Request-ID: req_12345678`
- `X-Service: ratings-and-reviews`
- `X-Version: v1`

## Best Practices

1. **Always** include the `meta` object with complete information
2. **Never** expose sensitive information in error messages
3. **Use** descriptive and user-friendly messages
4. **Maintain** consistent field names across all endpoints
5. **Include** proper HTTP status codes matching the response status
6. **Validate** all responses against this structure before deployment

---

*See also: [Error Codes and Messages](error_codes.md), [Pagination](pagination.md)*
