# API Pagination

## Introduction

This document defines the pagination standards for APIs that return large datasets. Proper pagination ensures optimal performance, predictable resource usage, and a consistent user experience across all our services.

## Pagination Principles

1. **Performance**: Limit response sizes to maintain fast response times
2. **Consistency**: Use the same pagination pattern across all APIs
3. **Flexibility**: Support different pagination strategies based on data source
4. **Predictability**: Provide clear navigation information
5. **Efficiency**: Minimize database load and network overhead

## Pagination Methods

### 1. Offset-Based Pagination (Default)

Best for: PostgreSQL databases, small to medium datasets

```http
GET /api/v1/reviews?page=2&per_page=20
```

**Response Structure:**
```json
{
  "status": "success",
  "message": "Reviews retrieved successfully",
  "data": [...],
  "pagination": {
    "current_page": 2,
    "per_page": 20,
    "total": 150,
    "total_pages": 8,
    "has_next": true,
    "has_previous": true,
    "next_page_url": "https://api.example.com/v1/reviews?page=3&per_page=20",
    "previous_page_url": "https://api.example.com/v1/reviews?page=1&per_page=20",
    "first_page_url": "https://api.example.com/v1/reviews?page=1&per_page=20",
    "last_page_url": "https://api.example.com/v1/reviews?page=8&per_page=20"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### 2. Cursor-Based Pagination

Best for: DynamoDB, large datasets, real-time data

```http
GET /api/v1/reviews?cursor=eyJpZCI6InJldl8xMjM0NTYifQ&limit=20
```

**Response Structure:**
```json
{
  "status": "success",
  "message": "Reviews retrieved successfully",
  "data": [...],
  "pagination": {
    "limit": 20,
    "has_next": true,
    "has_previous": true,
    "next_cursor": "eyJpZCI6InJldl8xMjM0ODkifQ",
    "previous_cursor": "eyJpZCI6InJldl8xMjM0MzIifQ",
    "next_page_url": "https://api.example.com/v1/reviews?cursor=eyJpZCI6InJldl8xMjM0ODkifQ&limit=20",
    "previous_page_url": "https://api.example.com/v1/reviews?cursor=eyJpZCI6InJldl8xMjM0MzIifQ&limit=20"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### 3. Seek-Based Pagination

Best for: Time-series data, ordered datasets

```http
GET /api/v1/reviews?since_id=rev_123456&limit=20
```

**Response Structure:**
```json
{
  "status": "success",
  "message": "Reviews retrieved successfully",
  "data": [...],
  "pagination": {
    "limit": 20,
    "since_id": "rev_123456",
    "until_id": "rev_123489",
    "has_next": true,
    "next_page_url": "https://api.example.com/v1/reviews?since_id=rev_123489&limit=20"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

## Parameter Standards

### Query Parameters

| Parameter | Type | Default | Max | Description |
|-----------|------|---------|-----|-------------|
| `page` | integer | 1 | 1000 | Page number (1-indexed) |
| `per_page` | integer | 15 | 100 | Items per page |
| `limit` | integer | 15 | 100 | Items to return (cursor-based) |
| `cursor` | string | null | - | Cursor token for pagination |
| `since_id` | string | null | - | Return items after this ID |
| `until_id` | string | null | - | Return items before this ID |

### Default Values

```json
{
  "default_pagination": {
    "per_page": 15,
    "max_per_page": 100,
    "max_page": 1000
  }
}
```

## Implementation Examples

### PostgreSQL (Offset-Based)

```php
class ReviewController
{
    public function index(Request $request)
    {
        $perPage = min($request->get('per_page', 15), 100);
        $page = min($request->get('page', 1), 1000);
        
        $reviews = Review::query()
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);
            
        return response()->json([
            'status' => 'success',
            'message' => 'Reviews retrieved successfully',
            'data' => $reviews->items(),
            'pagination' => [
                'current_page' => $reviews->currentPage(),
                'per_page' => $reviews->perPage(),
                'total' => $reviews->total(),
                'total_pages' => $reviews->lastPage(),
                'has_next' => $reviews->hasMorePages(),
                'has_previous' => $reviews->currentPage() > 1,
                'next_page_url' => $reviews->nextPageUrl(),
                'previous_page_url' => $reviews->previousPageUrl(),
                'first_page_url' => $reviews->url(1),
                'last_page_url' => $reviews->url($reviews->lastPage())
            ]
        ]);
    }
}
```

### DynamoDB (Cursor-Based)

```php
class ReviewController
{
    public function index(Request $request)
    {
        $limit = min($request->get('limit', 15), 100);
        $cursor = $request->get('cursor');
        
        $query = [
            'TableName' => 'reviews',
            'Limit' => $limit,
            'ScanIndexForward' => false // Descending order
        ];
        
        if ($cursor) {
            $query['ExclusiveStartKey'] = json_decode(
                base64_decode($cursor), 
                true
            );
        }
        
        $result = $this->dynamodb->scan($query);
        
        $nextCursor = null;
        if (isset($result['LastEvaluatedKey'])) {
            $nextCursor = base64_encode(
                json_encode($result['LastEvaluatedKey'])
            );
        }
        
        return response()->json([
            'status' => 'success',
            'message' => 'Reviews retrieved successfully',
            'data' => $result['Items'],
            'pagination' => [
                'limit' => $limit,
                'has_next' => !empty($nextCursor),
                'next_cursor' => $nextCursor,
                'next_page_url' => $nextCursor ? 
                    url("/api/v1/reviews?cursor={$nextCursor}&limit={$limit}") : 
                    null
            ]
        ]);
    }
}
```

### JavaScript/Node.js Example

```javascript
// Offset-based pagination
async function getReviews({ page = 1, perPage = 15 } = {}) {
  const response = await fetch(
    `/api/v1/reviews?page=${page}&per_page=${perPage}`
  );
  
  if (!response.ok) {
    throw new Error('Failed to fetch reviews');
  }
  
  return await response.json();
}

// Cursor-based pagination
async function getReviewsCursor({ cursor = null, limit = 15 } = {}) {
  const params = new URLSearchParams({ limit: limit.toString() });
  
  if (cursor) {
    params.append('cursor', cursor);
  }
  
  const response = await fetch(`/api/v1/reviews?${params}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch reviews');
  }
  
  return await response.json();
}

// Usage example
async function loadAllReviews() {
  const allReviews = [];
  let cursor = null;
  
  do {
    const response = await getReviewsCursor({ cursor, limit: 50 });
    allReviews.push(...response.data);
    cursor = response.pagination.next_cursor;
  } while (cursor);
  
  return allReviews;
}
```

## Error Handling

### Invalid Page Number

```json
{
  "status": "error",
  "message": "Invalid page number",
  "data": null,
  "error_code": "INVALID_PAGINATION_PARAMETER",
  "error_details": {
    "parameter": "page",
    "value": 1001,
    "max_allowed": 1000
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### Invalid Page Size

```json
{
  "status": "error",
  "message": "Invalid page size",
  "data": null,
  "error_code": "INVALID_PAGINATION_PARAMETER",
  "error_details": {
    "parameter": "per_page",
    "value": 150,
    "max_allowed": 100
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### Invalid Cursor

```json
{
  "status": "error",
  "message": "Invalid cursor token",
  "data": null,
  "error_code": "INVALID_CURSOR",
  "error_details": {
    "cursor": "invalid_cursor_token"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

## Performance Considerations

### Database Optimization

#### PostgreSQL

```sql
-- Ensure proper indexing for pagination
CREATE INDEX CONCURRENTLY idx_reviews_created_at_id 
ON reviews (created_at DESC, id DESC);

-- For filtered pagination
CREATE INDEX CONCURRENTLY idx_reviews_product_created_at 
ON reviews (product_id, created_at DESC);
```

#### DynamoDB

```json
{
  "TableName": "reviews",
  "KeySchema": [
    {
      "AttributeName": "product_id",
      "KeyType": "HASH"
    },
    {
      "AttributeName": "created_at",
      "KeyType": "RANGE"
    }
  ],
  "GlobalSecondaryIndexes": [
    {
      "IndexName": "CreatedAtIndex",
      "KeySchema": [
        {
          "AttributeName": "created_at",
          "KeyType": "HASH"
        }
      ]
    }
  ]
}
```

### Caching Strategy

```php
class ReviewController
{
    public function index(Request $request)
    {
        $cacheKey = "reviews:" . md5(serialize($request->query()));
        
        return Cache::remember($cacheKey, 300, function () use ($request) {
            return $this->getPaginatedReviews($request);
        });
    }
}
```

### Rate Limiting

```http
# Apply stricter limits for large page sizes
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Per-Page-Cost: 5  # Cost increases with page size
```

## Client-Side Best Practices

### Progressive Loading

```javascript
class ReviewsPaginator {
  constructor(apiClient) {
    this.apiClient = apiClient;
    this.currentPage = 1;
    this.hasNextPage = true;
    this.loading = false;
  }
  
  async loadNextPage() {
    if (!this.hasNextPage || this.loading) {
      return null;
    }
    
    this.loading = true;
    
    try {
      const response = await this.apiClient.getReviews({
        page: this.currentPage,
        per_page: 20
      });
      
      this.currentPage++;
      this.hasNextPage = response.pagination.has_next;
      
      return response.data;
    } finally {
      this.loading = false;
    }
  }
}
```

### Infinite Scroll Implementation

```javascript
function useInfiniteReviews() {
  const [reviews, setReviews] = useState([]);
  const [cursor, setCursor] = useState(null);
  const [loading, setLoading] = useState(false);
  const [hasNext, setHasNext] = useState(true);
  
  const loadMore = useCallback(async () => {
    if (loading || !hasNext) return;
    
    setLoading(true);
    
    try {
      const response = await fetch(
        `/api/v1/reviews?cursor=${cursor || ''}&limit=20`
      );
      const data = await response.json();
      
      setReviews(prev => [...prev, ...data.data]);
      setCursor(data.pagination.next_cursor);
      setHasNext(!!data.pagination.next_cursor);
    } finally {
      setLoading(false);
    }
  }, [cursor, loading, hasNext]);
  
  return { reviews, loading, hasNext, loadMore };
}
```

## Filtering and Sorting with Pagination

### Combined Parameters

```http
GET /api/v1/reviews?product_id=prod_123&rating=5&sort=created_at&order=desc&page=2&per_page=20
```

### Implementation

```php
public function index(Request $request)
{
    $query = Review::query();
    
    // Apply filters
    if ($request->has('product_id')) {
        $query->where('product_id', $request->get('product_id'));
    }
    
    if ($request->has('rating')) {
        $query->where('rating', $request->get('rating'));
    }
    
    // Apply sorting
    $sortField = $request->get('sort', 'created_at');
    $sortOrder = $request->get('order', 'desc');
    $query->orderBy($sortField, $sortOrder);
    
    // Apply pagination
    $perPage = min($request->get('per_page', 15), 100);
    $reviews = $query->paginate($perPage);
    
    return $this->formatPaginatedResponse($reviews);
}
```

## Testing Pagination

### Unit Tests

```php
class PaginationTest extends TestCase
{
    /** @test */
    public function it_returns_paginated_reviews()
    {
        Review::factory()->count(50)->create();
        
        $response = $this->get('/api/v1/reviews?page=2&per_page=10');
        
        $response->assertOk()
            ->assertJsonStructure([
                'data',
                'pagination' => [
                    'current_page',
                    'per_page',
                    'total',
                    'has_next',
                    'has_previous'
                ]
            ]);
            
        $this->assertEquals(2, $response->json('pagination.current_page'));
        $this->assertEquals(10, count($response->json('data')));
    }
    
    /** @test */
    public function it_validates_pagination_parameters()
    {
        $response = $this->get('/api/v1/reviews?page=1001');
        
        $response->assertStatus(422)
            ->assertJson([
                'error_code' => 'INVALID_PAGINATION_PARAMETER'
            ]);
    }
}
```

### Performance Tests

```javascript
describe('Pagination Performance', () => {
  test('should handle large datasets efficiently', async () => {
    const startTime = performance.now();
    
    const response = await fetch('/api/v1/reviews?page=100&per_page=50');
    
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    expect(response.status).toBe(200);
    expect(responseTime).toBeLessThan(1000); // Less than 1 second
  });
});
```

## Monitoring and Metrics

### Key Metrics

1. **Average Page Size**: Monitor typical page sizes requested
2. **Page Access Patterns**: Which pages are accessed most frequently
3. **Cursor Performance**: How efficiently cursor pagination performs
4. **Deep Pagination Usage**: How often users access high page numbers

### Alerting

```json
{
  "pagination_alerts": {
    "high_page_numbers": {
      "threshold": 500,
      "action": "log_warning"
    },
    "large_page_sizes": {
      "threshold": 80,
      "action": "rate_limit"
    },
    "slow_pagination_queries": {
      "threshold": "2s",
      "action": "investigate"
    }
  }
}
```

---

*See also: [Response Structures](response_structures.md), [Performance Guidelines](performance.md)*
