# Mumzworld API Documentation MCP Server

A Model Context Protocol (MCP) server that provides AI agents with access to Mumzworld's API rule books documentation. This server enables intelligent querying and searching of API standards, response structures, error codes, authentication methods, versioning strategies, and pagination guidelines.

## 🚀 Features

### Resources
- **Overview**: Get a complete overview of all available API documentation
- **Individual Documents**: Access specific documentation files by name

### Tools
- **Search Documentation**: Search across all documentation with relevance scoring
- **API Standards Summary**: Get a structured summary of all API standards
- **Code Examples**: Extract code examples for specific topics
- **List Files**: Get metadata about all documentation files

## 📁 Project Structure

```
.
├── api_docs_server.py          # Main MCP server implementation
├── requirements.txt            # Python dependencies  
├── README.md                   # This file
├── .github/
│   └── copilot-instructions.md # Copilot-specific instructions
├── .vscode/
│   └── mcp.json               # VS Code MCP configuration
└── ../                        # Parent directory contains API docs:
    ├── overview.md            # API documentation overview
    ├── response_structures.md # Response format standards
    ├── error_codes.md         # Error handling reference
    ├── authentication.md      # Authentication patterns
    ├── versioning.md          # API version management
    └── pagination.md          # Pagination standards
```

## 🛠 Installation

1. **Create Virtual Environment**:
   ```bash
   python3 -m venv mcp-env
   source mcp-env/bin/activate  # On Windows: mcp-env\Scripts\activate
   ```

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify Installation**:
   ```bash
   fastmcp version
   ```

## 🚀 Usage

### Configuration

The MCP server can be configured to point to different documentation directories:

#### Method 1: Command Line Arguments

```bash
# Use default path (parent directory)
python api_docs_server.py

# Specify custom documentation path
python api_docs_server.py --docs-path /path/to/your/docs

# Test the configuration
python api_docs_server.py --test --docs-path /path/to/your/docs

# List available documentation files
python api_docs_server.py --list-docs
```

#### Method 2: Environment Variables

```bash
# Set environment variable
export API_DOCS_PATH="/path/to/your/docs"
python api_docs_server.py

# Or use .env file
cp .env.example .env
# Edit .env file to set API_DOCS_PATH
python api_docs_server.py
```

#### Method 3: VS Code MCP Configuration

Update `.vscode/mcp.json`:

```json
{
  "servers": {
    "mumzworld-api-docs": {
      "type": "stdio",
      "command": "python",
      "args": ["api_docs_server.py", "--docs-path", "/path/to/your/docs"],
      "env": {
        "API_DOCS_PATH": "/path/to/your/docs"
      }
    }
  }
}
```

### Running the Server

```bash
# Activate virtual environment
source mcp-env/bin/activate

# Run the server
python api_docs_server.py
```

### Available Resources

1. **Get Overview**:
   ```
   Resource: api-docs/overview
   Description: Complete overview of all API documentation
   ```

2. **Get Specific Document**:
   ```
   Resource: api-docs/{file_name}
   Example: api-docs/response_structures.md
   Description: Full content of a specific documentation file
   ```

### Available Tools

1. **Search Documentation**:
   ```python
   search_documentation(query="authentication", max_results=5)
   ```
   - Searches all documentation for relevant content
   - Returns ranked results with relevance scores
   - Supports natural language queries

2. **Get API Standards Summary**:
   ```python
   get_api_standards_summary()
   ```
   - Returns structured summary of all API standards
   - Organized by categories (response structures, error codes, etc.)

3. **Get Code Examples**:
   ```python
   get_code_examples(topic="pagination")
   ```
   - Extracts code examples for specific topics
   - Returns examples with language and context

4. **List Documentation Files**:
   ```python
   list_documentation_files()
   ```
   - Returns metadata about all documentation files
   - Includes titles, sections, and file information

## 🔧 Configuration

### VS Code Integration

The server includes a `.vscode/mcp.json` configuration file for VS Code integration:

```json
{
  "servers": {
    "mumzworld-api-docs": {
      "type": "stdio", 
      "command": "python",
      "args": ["api_docs_server.py"]
    }
  }
}
```

### MCP Client Configuration

To use this server with an MCP client, add the following to your client configuration:

```json
{
  "mumzworld-api-docs": {
    "type": "stdio",
    "command": "python",
    "args": ["/path/to/api_docs_server.py"],
    "cwd": "/path/to/project/directory"
  }
}
```

## 📖 Example Queries

### Search for Authentication Information
```python
# Query: "How do I implement JWT authentication?"
search_documentation("JWT authentication implementation")
```

### Get Error Code Reference
```python
# Query: "What error codes should I use for validation?"
search_documentation("validation error codes")
```

### Find Pagination Examples
```python
# Query: "Show me pagination examples for DynamoDB"
get_code_examples("DynamoDB pagination")
```

### Get Response Structure Standards
```python
# Query: "What is the standard API response format?"
search_documentation("standard response structure")
```

## 🔍 Search Capabilities

The search functionality uses a scoring algorithm that considers:

- **Exact phrase matches** (highest score)
- **Individual keyword matches**
- **Section title relevance**
- **File name relevance**
- **Content context**

Results are ranked by relevance and limited to the most useful matches.

## 🐛 Debugging

### VS Code Debugging

1. Open the project in VS Code
2. The MCP server can be debugged using the integrated VS Code debugger
3. Set breakpoints in `api_docs_server.py` as needed
4. Use the debug configuration for Python scripts

### Manual Testing

```bash
# Test the server directly
python api_docs_server.py

# Test with FastMCP CLI (if available)
fastmcp test api_docs_server.py
```

## 📝 Adding New Documentation

To add new documentation files:

1. Place markdown files in the parent directory (same level as `overview.md`)
2. Follow the existing naming convention (lowercase with underscores)
3. Use standard markdown headers for section organization
4. The server will automatically detect and index new files

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Add type hints for all functions
3. Include docstrings for public methods
4. Test changes with various query types
5. Update this README if adding new features

## 📄 License

This project is part of the Mumzworld microservices documentation system.

---

**Built with FastMCP** - The fast, Pythonic way to build MCP servers and clients.
