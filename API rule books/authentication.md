# API Authentication

## Introduction

This document outlines the authentication mechanisms used across our API ecosystem. Proper authentication ensures secure access to resources while maintaining a seamless developer experience.

## Authentication Methods

Our APIs support multiple authentication methods depending on the use case and security requirements.

### 1. API Key Authentication

API keys are used for server-to-server communication and service identification.

#### Implementation

```http
GET /api/v1/reviews
Authorization: Bearer api_key_here
```

#### API Key Format

```
api_mumz_live_1234567890abcdef1234567890abcdef
api_mumz_test_1234567890abcdef1234567890abcdef
```

Format breakdown:
- `api_mumz_` - Prefix identifying the service
- `live_` or `test_` - Environment identifier
- `1234567890abcdef...` - 32-character random string

#### Headers Required

```http
Authorization: Bearer {api_key}
X-API-Version: v1
Content-Type: application/json
```

### 2. JWT Token Authentication

JWT tokens are used for user authentication and session management.

#### Token Structure

```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyXzEyMzQ1NiIsImlhdCI6MTYyNzgwNDgwMCwiZXhwIjoxNjI3ODkxMjAwfQ.signature
```

#### Implementation

```http
GET /api/v1/reviews/user/me
Authorization: Bearer {jwt_token}
```

#### Token Claims

```json
{
  "sub": "user_123456",
  "iat": 1627804800,
  "exp": 1627891200,
  "aud": "ratings-and-reviews",
  "iss": "mumz-auth-service",
  "scope": ["reviews:read", "reviews:write"],
  "user_id": "user_123456",
  "email": "<EMAIL>",
  "role": "customer"
}
```

### 3. OAuth 2.0

OAuth 2.0 is used for third-party integrations and mobile applications.

#### Supported Grant Types

1. **Authorization Code Flow** (for web applications)
2. **Client Credentials Flow** (for service-to-service)
3. **Resource Owner Password Credentials** (deprecated, legacy only)

#### Authorization Code Flow

```http
# Step 1: Authorization Request
GET /oauth/authorize?
  response_type=code&
  client_id=your_client_id&
  redirect_uri=https://yourapp.com/callback&
  scope=reviews:read reviews:write&
  state=random_state_string

# Step 2: Token Exchange
POST /oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&
code=received_auth_code&
client_id=your_client_id&
client_secret=your_client_secret&
redirect_uri=https://yourapp.com/callback
```

#### Client Credentials Flow

```http
POST /oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic base64(client_id:client_secret)

grant_type=client_credentials&
scope=reviews:read
```

## Scopes and Permissions

### Available Scopes

| Scope | Description | Allows |
|-------|-------------|--------|
| `reviews:read` | Read reviews | GET operations on reviews |
| `reviews:write` | Create/update reviews | POST, PUT, PATCH operations |
| `reviews:delete` | Delete reviews | DELETE operations |
| `reviews:admin` | Administrative access | All operations + admin endpoints |
| `statistics:read` | Read statistics | GET operations on statistics |
| `user:profile` | Access user profile | User profile operations |

### Permission Matrix

| Role | Scopes | Description |
|------|--------|-------------|
| **Customer** | `reviews:read`, `reviews:write` | Can read all reviews, write own reviews |
| **Moderator** | `reviews:read`, `reviews:write`, `reviews:delete` | Can moderate content |
| **Admin** | `reviews:admin`, `statistics:read` | Full access to all operations |
| **Service** | `reviews:read`, `statistics:read` | Read-only access for integrations |

## Authentication Flow Examples

### User Authentication Flow

```mermaid
sequenceDiagram
    participant Client
    participant Auth Service
    participant API Gateway
    participant Reviews Service

    Client->>Auth Service: Login request
    Auth Service-->>Client: JWT token
    Client->>API Gateway: API request + JWT token
    API Gateway->>Auth Service: Validate token
    Auth Service-->>API Gateway: Token valid + user claims
    API Gateway->>Reviews Service: Forward request + user context
    Reviews Service-->>API Gateway: Response
    API Gateway-->>Client: API response
```

### Service-to-Service Authentication

```mermaid
sequenceDiagram
    participant Service A
    participant Auth Service
    participant Service B

    Service A->>Auth Service: Client credentials
    Auth Service-->>Service A: Access token
    Service A->>Service B: API request + access token
    Service B->>Auth Service: Validate token
    Auth Service-->>Service B: Token valid
    Service B-->>Service A: API response
```

## Error Responses

### Invalid Authentication

```json
{
  "status": "error",
  "message": "Authentication required",
  "data": null,
  "error_code": "UNAUTHORIZED",
  "error_details": {
    "reason": "missing_authorization_header"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### Invalid Token

```json
{
  "status": "error",
  "message": "Invalid authentication token",
  "data": null,
  "error_code": "INVALID_TOKEN",
  "error_details": {
    "reason": "token_expired",
    "expired_at": "2025-07-31T11:00:00Z"
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

### Insufficient Permissions

```json
{
  "status": "error",
  "message": "Insufficient permissions for this action",
  "data": null,
  "error_code": "INSUFFICIENT_PERMISSIONS",
  "error_details": {
    "required_scope": "reviews:admin",
    "current_scopes": ["reviews:read", "reviews:write"]
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "request_id": "req_12345678",
    "service": "ratings-and-reviews",
    "version": "v1",
    "environment": "production"
  }
}
```

## Security Best Practices

### For API Providers

1. **Use HTTPS everywhere** - Never transmit credentials over HTTP
2. **Validate all tokens** - Always verify token signature and expiration
3. **Implement rate limiting** - Prevent brute force attacks
4. **Log authentication events** - Monitor for suspicious activity
5. **Rotate secrets regularly** - Update API keys and signing keys periodically
6. **Use short-lived tokens** - Implement token refresh mechanisms
7. **Validate scopes** - Check permissions for each operation

### For API Consumers

1. **Store credentials securely** - Never commit credentials to version control
2. **Use environment variables** - Store secrets in secure configuration
3. **Implement token refresh** - Handle token expiration gracefully
4. **Handle errors properly** - Implement proper error handling for auth failures
5. **Use PKCE for OAuth** - Implement Proof Key for Code Exchange for mobile apps
6. **Validate SSL certificates** - Ensure secure connections

## Implementation Examples

### PHP Laravel Implementation

```php
// Middleware for API key authentication
class ApiKeyAuthentication
{
    public function handle($request, Closure $next)
    {
        $apiKey = $request->bearerToken();
        
        if (!$apiKey || !$this->validateApiKey($apiKey)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid API key',
                'data' => null,
                'error_code' => 'INVALID_TOKEN'
            ], 401);
        }
        
        return $next($request);
    }
}

// JWT token validation
class JwtAuthentication
{
    public function handle($request, Closure $next)
    {
        try {
            $token = $request->bearerToken();
            $payload = JWT::decode($token, $this->getPublicKey(), ['RS256']);
            
            $request->merge(['user_context' => $payload]);
            
            return $next($request);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid token',
                'data' => null,
                'error_code' => 'INVALID_TOKEN'
            ], 401);
        }
    }
}
```

### JavaScript/Node.js Implementation

```javascript
// API key authentication
const authenticateApiKey = (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      status: 'error',
      message: 'Authentication required',
      data: null,
      error_code: 'UNAUTHORIZED'
    });
  }
  
  const apiKey = authHeader.substring(7);
  
  if (!validateApiKey(apiKey)) {
    return res.status(401).json({
      status: 'error',
      message: 'Invalid API key',
      data: null,
      error_code: 'INVALID_TOKEN'
    });
  }
  
  next();
};

// JWT token validation
const authenticateJWT = (req, res, next) => {
  const token = req.headers.authorization?.substring(7);
  
  if (!token) {
    return res.status(401).json({
      status: 'error',
      message: 'Authentication required',
      data: null,
      error_code: 'UNAUTHORIZED'
    });
  }
  
  jwt.verify(token, process.env.JWT_SECRET, (err, decoded) => {
    if (err) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid token',
        data: null,
        error_code: 'INVALID_TOKEN'
      });
    }
    
    req.user = decoded;
    next();
  });
};
```

## Testing Authentication

### cURL Examples

```bash
# API Key authentication
curl -X GET "https://api.example.com/v1/reviews" \
  -H "Authorization: Bearer api_mumz_live_1234567890abcdef" \
  -H "Content-Type: application/json"

# JWT token authentication
curl -X GET "https://api.example.com/v1/reviews/user/me" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json"

# OAuth token exchange
curl -X POST "https://api.example.com/oauth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Basic $(echo -n 'client_id:client_secret' | base64)" \
  -d "grant_type=client_credentials&scope=reviews:read"
```

### Postman Collection

```json
{
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{access_token}}",
        "type": "string"
      }
    ]
  },
  "event": [
    {
      "listen": "prerequest",
      "script": {
        "exec": [
          "// Auto-refresh token if expired",
          "if (pm.globals.get('token_expires_at') < Date.now()) {",
          "  // Refresh token logic here",
          "}"
        ]
      }
    }
  ]
}
```

## Monitoring and Analytics

### Key Metrics to Track

1. **Authentication Success Rate** - Percentage of successful authentications
2. **Token Validation Latency** - Time taken to validate tokens
3. **Failed Authentication Attempts** - Track potential security threats
4. **Token Refresh Rate** - How often tokens are refreshed
5. **API Key Usage** - Monitor API key usage patterns

### Alerting Rules

- Alert when authentication failure rate > 10%
- Alert when token validation latency > 500ms
- Alert when failed attempts from single IP > 100/hour
- Alert when new API key is created
- Alert when API key is revoked

---

*See also: [Error Codes and Messages](error_codes.md), [Rate Limiting](rate_limiting.md)*