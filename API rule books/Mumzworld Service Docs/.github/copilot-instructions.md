<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

# Copilot Instructions for API Documentation MCP Server

This is an MCP (Model Context Protocol) server project for the Mumzworld API Documentation system.

## Project Overview
- **Purpose**: Provide AI agents access to API rule books documentation
- **Framework**: FastMCP (Python-based MCP server framework)
- **Documentation**: Serves Mumzworld API standards including response structures, error codes, authentication, versioning, and pagination

## Key Guidelines
1. When working with this MCP server, always ensure compatibility with the FastMCP framework
2. Follow the MCP protocol specifications for resources and tools
3. Maintain backward compatibility when updating server functions
4. All documentation parsing should handle markdown format gracefully
5. Search functionality should be optimized for API-related queries

## Useful Resources
- FastMCP Documentation: https://gofastmcp.com/
- MCP Protocol: https://modelcontextprotocol.io/
- You can find more info and examples at https://modelcontextprotocol.io/llms-full.txt

## File Structure
- `api_docs_server.py`: Main MCP server implementation
- `requirements.txt`: Python dependencies
- Parent directory contains the API documentation markdown files

## Testing
- Test all tools and resources with realistic API documentation queries
- Verify search functionality returns relevant results
- Ensure error handling for missing or corrupted documentation files
