# Generalized API Response Structure

## **Introduction**
In a microservices architecture, different services may use different databases (e.g., DynamoDB, PostgreSQL) and have unique requirements. However, the way these services communicate with clients (e.g., frontend, other services) should be consistent. This is where a **generalized API response structure** comes in.

The goal is to ensure that all APIs return responses in a predictable format, regardless of the underlying database or service logic. This makes it easier for developers to consume APIs, debug issues, and maintain consistency across the system.

---

## **Why Generalize API Responses?**
1. **Consistency**: A uniform structure ensures that developers know what to expect from every API.
2. **Ease of Integration**: Frontend and other services can handle responses without writing custom logic for each API.
3. **Scalability**: Adding new fields or metadata becomes easier without breaking existing clients.
4. **Debugging**: Standardized error codes and messages make it easier to identify and fix issues.

---

## **Proposed Response Structure**
Every API response will follow this structure:

```json
{
  "message": "Success or error message",
  "data": "The actual data returned by the API (can be an object, array, or null)",
  "pagination": {
    "current_page": 1,
    "per_page": 15,
    "total": 100,
    "next_page_url": "http://example.com/api/resource?page=2",
    "prev_page_url": null
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "service": "Service name",
    "environment": "production or staging"
  }
}
```

---

## **Key Components**
1. **`message`**:
   - A short description of the result (e.g., "Success", "Validation error", "Resource not found").
   - Helps clients understand the outcome without parsing the entire response.

2. **`data`**:
   - Contains the actual data returned by the API.
   - Can be an object, array, or `null` (e.g., for errors or empty results).

3. **`pagination`** (Optional):
   - Used for paginated responses (e.g., lists of items).
   - Includes details like the current page, items per page, total items, and navigation URLs.

4. **`meta`**:
   - Additional metadata about the response.
   - Includes a timestamp, service name, and environment details.

---

## **Success and Error Responses**

### **Success Response**
For successful operations, the `message` will indicate success, and the `data` field will contain the result.

Example:
```json
{
  "message": "Reviews fetched successfully",
  "data": [
    {
      "review_id": "123",
      "user_id": "456",
      "rating": 5,
      "comment": "Great product!"
    }
  ],
  "pagination": {
    "current_page": 1,
    "per_page": 15,
    "total": 100,
    "next_page_url": "http://example.com/api/reviews?page=2",
    "prev_page_url": null
  },
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "service": "Ratings and Reviews",
    "environment": "production"
  }
}
```

### **Error Response**
For errors, the `message` will describe the issue, and the `data` field will be `null`. You can also include an `error_code` for easier debugging.

Example:
```json
{
  "message": "Validation error",
  "data": null,
  "error_code": 422,
  "meta": {
    "timestamp": "2025-07-31T12:00:00Z",
    "service": "Ratings and Reviews",
    "environment": "production"
  }
}
```

---

## **Error Codes**
Here are some common error codes to standardize across services:

| **Code** | **Description**                  |
|----------|----------------------------------|
| 200      | Success                          |
| 201      | Resource created successfully    |
| 400      | Bad request                      |
| 401      | Unauthorized                     |
| 403      | Forbidden                        |
| 404      | Resource not found               |
| 422      | Validation error                 |
| 500      | Internal server error            |

---

## **Implementation Guidelines**

### **1. Create a Response Helper**
Each service should have a helper function or class to format responses. For example:

```php
class ResponseHelper
{
    public static function success($data = [], $message = 'Success', $pagination = null, $meta = []): array
    {
        return [
            'message' => $message,
            'data' => $data,
            'pagination' => $pagination,
            'meta' => array_merge([
                'timestamp' => now()->toIso8601String(),
                'service' => config('app.name'),
                'environment' => config('app.env'),
            ], $meta),
        ];
    }

    public static function error($message = 'Error', $code = 400, $meta = []): array
    {
        return [
            'message' => $message,
            'data' => null,
            'error_code' => $code,
            'meta' => array_merge([
                'timestamp' => now()->toIso8601String(),
                'service' => config('app.name'),
                'environment' => config('app.env'),
            ], $meta),
        ];
    }
}
```

### **2. Use the Helper in Controllers**
Replace direct responses in controllers with the helper:

```php
use App\Helpers\ResponseHelper;

public function getReviews(Request $request)
{
    $reviews = Review::paginate(15);

    return response()->json(
        ResponseHelper::success(
            $reviews->items(),
            'Reviews fetched successfully',
            [
                'current_page' => $reviews->currentPage(),
                'per_page' => $reviews->perPage(),
                'total' => $reviews->total(),
                'next_page_url' => $reviews->nextPageUrl(),
                'prev_page_url' => $reviews->previousPageUrl(),
            ]
        )
    );
}
```

### **3. Handle Pagination**
For DynamoDB, use `LastEvaluatedKey` for pagination. For PostgreSQL, use `LIMIT` and `OFFSET`. Ensure the `pagination` field is populated consistently.

---

## **Conclusion**
By adopting this generalized response structure, we can:
- Ensure consistency across all microservices.
- Simplify API consumption for clients.
- Make debugging and maintenance easier.

This approach is flexible enough to work with both DynamoDB and PostgreSQL, ensuring that our services remain scalable and easy to integrate.
