#!/usr/bin/env python3
"""
API Documentation MCP Server

This server provides access to the Mumzworld API rule books documentation,
allowing AI agents to query and search through API standards, response structures,
error codes, authentication methods, versioning strategies, and pagination guidelines.
"""

import os
import re
import argparse
from pathlib import Path
from typing import List, Optional, Dict, Any
import json

from fastmcp import FastMCP
from pydantic import BaseModel


# Global variable for docs path
API_DOCS_PATH = None


def get_docs_path() -> Path:
    """Get the documentation path from environment variable or command line argument"""
    global API_DOCS_PATH
    
    if API_DOCS_PATH is not None:
        return API_DOCS_PATH
    
    # Check environment variable first
    env_path = os.getenv('API_DOCS_PATH')
    if env_path:
        API_DOCS_PATH = Path(env_path).resolve()
        return API_DOCS_PATH
    
    # Default to parent directory (same as before)
    API_DOCS_PATH = Path(__file__).parent.parent
    return API_DOCS_PATH


def set_docs_path(path: str):
    """Set the documentation path"""
    global API_DOCS_PATH
    API_DOCS_PATH = Path(path).resolve()


# Initialize the MCP server
mcp = FastMCP("Mumzworld API Documentation Server")

class DocumentSearchResult(BaseModel):
    """Result from searching documentation"""
    file_name: str
    title: str
    section: str
    content: str
    relevance_score: float

class DocumentInfo(BaseModel):
    """Information about a documentation file"""
    file_name: str
    title: str
    sections: List[str]
    file_path: str
    size: int


def get_all_docs() -> List[Path]:
    """Get all markdown files in the API rule books directory"""
    docs = []
    docs_path = get_docs_path()
    for file_path in docs_path.glob("*.md"):
        if file_path.is_file():
            docs.append(file_path)
    return docs


def parse_markdown_sections(content: str) -> Dict[str, str]:
    """Parse markdown content into sections based on headers"""
    sections = {}
    current_section = "Introduction"
    current_content = []
    
    lines = content.split('\n')
    
    for line in lines:
        # Check if line is a header (starts with #)
        if line.strip().startswith('#'):
            # Save previous section
            if current_content:
                sections[current_section] = '\n'.join(current_content).strip()
            
            # Start new section
            current_section = line.strip().lstrip('#').strip()
            current_content = []
        else:
            current_content.append(line)
    
    # Save last section
    if current_content:
        sections[current_section] = '\n'.join(current_content).strip()
    
    return sections


def search_content(query: str, content: str, file_name: str, section: str) -> float:
    """Calculate relevance score for content based on query"""
    query_lower = query.lower()
    content_lower = content.lower()
    
    # Simple scoring based on keyword matches
    score = 0.0
    
    # Exact phrase match (highest score)
    if query_lower in content_lower:
        score += 10.0
    
    # Individual word matches
    query_words = query_lower.split()
    for word in query_words:
        if word in content_lower:
            score += 2.0
    
    # Boost score for matches in titles/headers
    if any(word in section.lower() for word in query_words):
        score += 5.0
    
    # Boost score for matches in file names
    if any(word in file_name.lower() for word in query_words):
        score += 3.0
    
    return score


@mcp.resource("file://api-docs/overview")
def get_overview() -> str:
    """Get an overview of all available API documentation"""
    docs = get_all_docs()
    overview = "# Mumzworld API Documentation Overview\n\n"
    overview += "Available documentation files:\n\n"
    
    for doc_path in docs:
        try:
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # Extract title from first header
                title = doc_path.stem.replace('_', ' ').title()
                first_line = content.split('\n')[0].strip()
                if first_line.startswith('#'):
                    title = first_line.lstrip('#').strip()
                
                overview += f"- **{title}** (`{doc_path.name}`)\n"
                
                # Add brief description from first paragraph
                lines = content.split('\n')
                for line in lines[1:]:
                    if line.strip() and not line.startswith('#'):
                        overview += f"  {line.strip()[:100]}...\n"
                        break
                overview += "\n"
        except Exception as e:
            overview += f"- {doc_path.name} (Error reading: {str(e)})\n"
    
    return overview


@mcp.resource("file://api-docs/{file_name}")
def get_document(file_name: str) -> str:
    """Get the full content of a specific documentation file"""
    # Clean the file name and ensure it has .md extension
    if not file_name.endswith('.md'):
        file_name += '.md'
    
    docs_path = get_docs_path()
    doc_path = docs_path / file_name
    
    if not doc_path.exists():
        return f"Documentation file '{file_name}' not found. Available files: {[f.name for f in get_all_docs()]}"
    
    try:
        with open(doc_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        return f"Error reading file '{file_name}': {str(e)}"


@mcp.tool
def search_documentation(query: str, max_results: int = 5) -> List[DocumentSearchResult]:
    """
    Search through all API documentation for relevant content.
    
    Args:
        query: The search query (e.g., "error codes", "authentication", "pagination")
        max_results: Maximum number of results to return (default: 5)
    
    Returns:
        List of search results with relevance scores
    """
    results = []
    docs = get_all_docs()
    
    for doc_path in docs:
        try:
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Extract title
            title = doc_path.stem.replace('_', ' ').title()
            first_line = content.split('\n')[0].strip()
            if first_line.startswith('#'):
                title = first_line.lstrip('#').strip()
            
            # Parse into sections
            sections = parse_markdown_sections(content)
            
            # Search each section
            for section_name, section_content in sections.items():
                if section_content.strip():
                    score = search_content(query, section_content, doc_path.name, section_name)
                    
                    if score > 0:
                        results.append(DocumentSearchResult(
                            file_name=doc_path.name,
                            title=title,
                            section=section_name,
                            content=section_content[:500] + "..." if len(section_content) > 500 else section_content,
                            relevance_score=score
                        ))
        except Exception as e:
            continue
    
    # Sort by relevance score and return top results
    results.sort(key=lambda x: x.relevance_score, reverse=True)
    return results[:max_results]


@mcp.tool
def get_api_standards_summary() -> Dict[str, Any]:
    """
    Get a summary of all API standards covered in the documentation.
    
    Returns:
        Dictionary with categories and their key points
    """
    summary = {
        "response_structures": [],
        "error_codes": [],
        "authentication": [],
        "versioning": [],
        "pagination": [],
        "overview": []
    }
    
    docs = get_all_docs()
    
    for doc_path in docs:
        try:
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            file_key = doc_path.stem.lower()
            
            # Extract key points based on file type
            if "response" in file_key or "structure" in file_key:
                # Extract response structure info
                if "Standard Response Structure" in content:
                    summary["response_structures"].append({
                        "source": doc_path.name,
                        "key_fields": ["status", "message", "data", "pagination", "meta"],
                        "note": "All APIs must follow this structure"
                    })
            
            elif "error" in file_key:
                # Extract error code categories
                if "Custom Error Codes" in content:
                    summary["error_codes"].append({
                        "source": doc_path.name,
                        "categories": ["Authentication & Authorization", "Validation", "Resource", "Rate Limiting", "Business Logic", "System"],
                        "note": "Standardized error codes across all services"
                    })
            
            elif "auth" in file_key:
                # Extract authentication methods
                if "Authentication Methods" in content:
                    summary["authentication"].append({
                        "source": doc_path.name,
                        "methods": ["API Key", "JWT Token", "OAuth 2.0"],
                        "note": "Multiple auth methods supported"
                    })
            
            elif "version" in file_key:
                # Extract versioning info
                if "Versioning Scheme" in content:
                    summary["versioning"].append({
                        "source": doc_path.name,
                        "scheme": "v{MAJOR}.{MINOR}.{PATCH}",
                        "methods": ["URL Path", "Header", "Query Parameter"],
                        "note": "Semantic versioning with backward compatibility"
                    })
            
            elif "pagination" in file_key:
                # Extract pagination methods
                if "Pagination Methods" in content:
                    summary["pagination"].append({
                        "source": doc_path.name,
                        "methods": ["Offset-based", "Cursor-based", "Seek-based"],
                        "note": "Different methods for different data sources"
                    })
            
            elif "overview" in file_key:
                # Extract overview info
                summary["overview"].append({
                    "source": doc_path.name,
                    "note": "Central hub with navigation and principles"
                })
                
        except Exception as e:
            continue
    
    return summary


@mcp.tool
def get_code_examples(topic: str) -> List[Dict[str, str]]:
    """
    Get code examples for a specific topic from the documentation.
    
    Args:
        topic: The topic to find examples for (e.g., "authentication", "pagination", "error handling")
    
    Returns:
        List of code examples with language and description
    """
    examples = []
    docs = get_all_docs()
    
    for doc_path in docs:
        try:
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Look for code blocks related to the topic
            lines = content.split('\n')
            in_code_block = False
            current_code = []
            current_language = ""
            
            for i, line in enumerate(lines):
                if line.strip().startswith('```'):
                    if in_code_block:
                        # End of code block
                        if current_code and topic.lower() in '\n'.join(lines[max(0, i-20):i]).lower():
                            # Find description before code block
                            description = ""
                            for j in range(max(0, i-10), i):
                                if lines[j].strip() and not lines[j].startswith('#'):
                                    description = lines[j].strip()
                                    break
                            
                            examples.append({
                                "language": current_language,
                                "code": '\n'.join(current_code),
                                "description": description,
                                "source": doc_path.name
                            })
                        
                        in_code_block = False
                        current_code = []
                        current_language = ""
                    else:
                        # Start of code block
                        in_code_block = True
                        current_language = line.strip()[3:].strip()
                elif in_code_block:
                    current_code.append(line)
        except Exception as e:
            continue
    
    return examples


@mcp.tool
def list_documentation_files() -> List[DocumentInfo]:
    """
    List all available documentation files with their metadata.
    
    Returns:
        List of documentation file information
    """
    docs = get_all_docs()
    file_list = []
    
    for doc_path in docs:
        try:
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract title
            title = doc_path.stem.replace('_', ' ').title()
            first_line = content.split('\n')[0].strip()
            if first_line.startswith('#'):
                title = first_line.lstrip('#').strip()
            
            # Extract sections
            sections = list(parse_markdown_sections(content).keys())
            
            file_list.append(DocumentInfo(
                file_name=doc_path.name,
                title=title,
                sections=sections,
                file_path=str(doc_path),
                size=len(content)
            ))
        except Exception as e:
            file_list.append(DocumentInfo(
                file_name=doc_path.name,
                title=f"Error reading {doc_path.name}",
                sections=[],
                file_path=str(doc_path),
                size=0
            ))
    
    return file_list


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="Mumzworld API Documentation MCP Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Use default path (parent directory)
  python api_docs_server.py
  
  # Specify custom documentation path
  python api_docs_server.py --docs-path /path/to/docs
  
  # Use environment variable
  API_DOCS_PATH=/path/to/docs python api_docs_server.py
  
  # Test the server configuration
  python api_docs_server.py --test
        """
    )
    
    parser.add_argument(
        '--docs-path', 
        type=str,
        help='Path to the directory containing API documentation markdown files'
    )
    
    parser.add_argument(
        '--test',
        action='store_true',
        help='Test the server configuration and exit'
    )
    
    parser.add_argument(
        '--list-docs',
        action='store_true', 
        help='List all available documentation files and exit'
    )
    
    args = parser.parse_args()
    
    # Update docs path if provided via command line
    if args.docs_path:
        set_docs_path(args.docs_path)
    
    # Test mode
    if args.test:
        docs_path = get_docs_path()
        print(f"🔧 Testing MCP Server Configuration")
        print(f"📁 Documentation Path: {docs_path}")
        print(f"📁 Path Exists: {docs_path.exists()}")
        
        if docs_path.exists():
            docs = get_all_docs()
            print(f"📚 Found {len(docs)} documentation files:")
            for doc in docs:
                print(f"   - {doc.name}")
            
            # Test search functionality
            if docs:
                print(f"\n🔍 Testing search on {docs[0].name}...")
                try:
                    with open(docs[0], 'r', encoding='utf-8') as f:
                        content = f.read()
                    sections = parse_markdown_sections(content)
                    print(f"   ✅ Successfully parsed {len(sections)} sections")
                except Exception as e:
                    print(f"   ❌ Error parsing file: {e}")
        else:
            print(f"❌ Documentation path does not exist!")
            print(f"💡 Make sure the path contains markdown (.md) files")
        
        exit(0)
    
    # List docs mode
    if args.list_docs:
        docs_path = get_docs_path()
        print(f"📚 Available Documentation Files in {docs_path}:")
        docs = get_all_docs()
        if docs:
            for doc in docs:
                try:
                    with open(doc, 'r', encoding='utf-8') as f:
                        first_line = f.readline().strip()
                    title = first_line.lstrip('#').strip() if first_line.startswith('#') else doc.stem
                    print(f"   📄 {doc.name} - {title}")
                except:
                    print(f"   📄 {doc.name}")
        else:
            print("   No documentation files found!")
        exit(0)
    
    # Print startup info
    docs_path = get_docs_path()
    print(f"🚀 Starting Mumzworld API Documentation MCP Server")
    print(f"📁 Documentation Path: {docs_path}")
    
    if not docs_path.exists():
        print(f"⚠️  Warning: Documentation path does not exist!")
        print(f"💡 Use --docs-path to specify a different path")
    else:
        docs = get_all_docs()
        print(f"📚 Loaded {len(docs)} documentation files")
    
    # Start the MCP server
    mcp.run()
